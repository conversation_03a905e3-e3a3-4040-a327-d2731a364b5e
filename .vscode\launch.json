{"version": "0.2.0", "configurations": [{"name": "Debug DeCap Training", "type": "python", "request": "launch", "program": "${workspaceFolder}/train.py", "console": "integratedTerminal", "args": ["--dataset", "coco", "--data", "data/coco_train.json", "--out_dir", "./output/debug", "--prefix", "debug_prefix", "--epochs", "1", "--bs", "4", "--save_every", "1"], "env": {"MASTER_ADDR": "localhost", "MASTER_PORT": "12355", "RANK": "0", "WORLD_SIZE": "1", "LOCAL_RANK": "0"}}]}