import json
import os
import numpy as np
from tqdm import tqdm
import argparse
from collections import defaultdict

# 尝试导入评估指标库
try:
    from pycocoevalcap.tokenizer.ptbtokenizer import PTBTokenizer
    from pycocoevalcap.bleu.bleu import Bleu
    from pycocoevalcap.meteor.meteor import Meteor
    from pycocoevalcap.cider.cider import Cider
    from pycocoevalcap.spice.spice import Spice
except ImportError:
    print("请安装pycocoevalcap库:")
    print("pip install pycocoevalcap")
    print("或者从GitHub克隆: https://github.com/salaniz/pycocoevalcap")
    raise

def calculate_metrics(reference_captions, generated_captions):
    """
    计算BLEU, METEOR, CIDEr, SPICE指标
    
    Args:
        reference_captions: dict, 格式为 {image_id: [caption1, caption2, ...]}
        generated_captions: dict, 格式为 {image_id: [caption]}
        
    Returns:
        dict: 包含各项指标的字典
    """
    # 初始化评估器
    tokenizer = PTBTokenizer()
    bleu_scorer = Bleu(n=4)
    meteor_scorer = Meteor()
    cider_scorer = Cider()
    spice_scorer = Spice()
    
    # 对参考描述和生成描述进行标记化
    reference_tokens = tokenizer.tokenize(reference_captions)
    generated_tokens = tokenizer.tokenize(generated_captions)
    
    # 计算各项指标
    print("计算BLEU指标...")
    bleu_scores, _ = bleu_scorer.compute_score(reference_tokens, generated_tokens)
    
    print("计算METEOR指标...")
    meteor_score, _ = meteor_scorer.compute_score(reference_tokens, generated_tokens)
    
    print("计算CIDEr指标...")
    cider_score, _ = cider_scorer.compute_score(reference_tokens, generated_tokens)
    
    print("计算SPICE指标...")
    spice_score, _ = spice_scorer.compute_score(reference_tokens, generated_tokens)
    
    # 整理结果
    metrics = {
        'BLEU-1': bleu_scores[0],
        'BLEU-2': bleu_scores[1],
        'BLEU-3': bleu_scores[2],
        'BLEU-4': bleu_scores[3],
        'METEOR': meteor_score,
        'CIDEr': cider_score,
        'SPICE': spice_score
    }
    
    return metrics

def prepare_captions_for_evaluation(reference_path, generated_path):
    """
    准备参考描述和生成描述用于评估
    
    Args:
        reference_path: str, 参考描述文件路径 (JSON格式)
        generated_path: str, 生成描述文件路径 (JSON格式)
        
    Returns:
        tuple: (reference_captions, generated_captions)
    """
    # 加载参考描述
    with open(reference_path, 'r', encoding='utf-8') as f:
        reference_data = json.load(f)
    
    # 加载生成描述
    with open(generated_path, 'r', encoding='utf-8') as f:
        generated_data = json.load(f)
    
    # 确保两个列表长度相同
    min_length = min(len(reference_data), len(generated_data))
    reference_data = reference_data[:min_length]
    generated_data = generated_data[:min_length]
    
    # 转换为评估所需的格式
    reference_captions = {}
    generated_captions = {}
    
    for i, (ref_cap, gen_cap) in enumerate(zip(reference_data, generated_data)):
        image_id = str(i)
        
        # 处理参考描述
        if isinstance(ref_cap, str):
            reference_captions[image_id] = [{'caption': ref_cap}]
        elif isinstance(ref_cap, list):
            reference_captions[image_id] = [{'caption': c} for c in ref_cap]
        
        # 处理生成描述
        if isinstance(gen_cap, str):
            # 清理生成的描述（移除特殊标记）
            gen_cap = gen_cap.replace('<|startoftext|>', '').replace('<|endoftext|>', '').strip()
            generated_captions[image_id] = [{'caption': gen_cap}]
        elif isinstance(gen_cap, list):
            gen_cap = [c.replace('<|startoftext|>', '').replace('<|endoftext|>', '').strip() for c in gen_cap]
            generated_captions[image_id] = [{'caption': c} for c in gen_cap]
    
    return reference_captions, generated_captions

def evaluate_captions(reference_path, generated_path, output_path=None):
    """
    评估生成的图像描述
    
    Args:
        reference_path: str, 参考描述文件路径
        generated_path: str, 生成描述文件路径
        output_path: str, 结果输出路径 (可选)
        
    Returns:
        dict: 包含各项指标的字典
    """
    print(f"参考描述文件: {reference_path}")
    print(f"生成描述文件: {generated_path}")
    
    # 准备数据
    reference_captions, generated_captions = prepare_captions_for_evaluation(
        reference_path, generated_path
    )
    
    print(f"共有 {len(reference_captions)} 对描述进行评估")
    
    # 计算指标
    metrics = calculate_metrics(reference_captions, generated_captions)
    
    # 打印结果
    print("\n" + "="*50)
    print("评估结果:")
    print("="*50)
    for metric, score in metrics.items():
        print(f"{metric}: {score:.4f}")
    
    # 保存结果
    if output_path:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(metrics, f, indent=2)
        print(f"\n结果已保存到: {output_path}")
    
    return metrics

def main():
    parser = argparse.ArgumentParser(description='评估图像描述生成质量')
    parser.add_argument('--reference', default = r"D:\project\DeCap\captions\nsd_cocotest_caption.json", help='参考描述文件路径 (JSON格式)')
    parser.add_argument('--generated', default = r"D:\project\DeCap\captions\fMRI_captions_sub1.json", help='生成描述文件路径 (JSON格式)')
    parser.add_argument('--output', default= r"D:\project\DeCap\result\metric", help='结果输出路径 (可选)')
    
    args = parser.parse_args()
    
    evaluate_captions(args.reference, args.generated, args.output)

if __name__ == "__main__":
    main()
