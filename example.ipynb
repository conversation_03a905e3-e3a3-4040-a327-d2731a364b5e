{"cells": [{"cell_type": "code", "execution_count": 1, "id": "4ff79820", "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m在当前单元格或上一个单元格中执行代码时 Kernel 崩溃。\n", "\u001b[1;31m请查看单元格中的代码，以确定故障的可能原因。\n", "\u001b[1;31m单击<a href='https://aka.ms/vscodeJupyterKernelCrash'>此处</a>了解详细信息。\n", "\u001b[1;31m有关更多详细信息，请查看 Jupyter <a href='command:jupyter.viewOutput'>log</a>。"]}], "source": ["import torch\n", "device = 'cuda:0'\n", "pretrain_map= {\n", "                'RN50':{'pretrained':'/opt/data/private/lq/clip/RN50.pt','resize':(224,224)}, #1024 \n", "                'RN101':{'pretrained':'/opt/data/private/lq/clip/RN101.pt','resize':(224,224)}, #512\n", "                'ViT-B-16':{'pretrained':'/opt/data/private/lq/huggingface/hub/models--laion--CLIP-ViT-B-16-laion2B-s34B-b88K/snapshots/7288da5a0d6f0b51c4a2b27c624837a9236d0112/open_clip_pytorch_model.bin','resize':(224,224)}, #512\n", "                'ViT-B-32':{'pretrained':'/opt/data/private/lq/huggingface/hub/models--laion--CLIP-ViT-B-32-laion2B-s34B-b79K/snapshots/08f73555f1b2fb7c82058aebbd492887a94968ef/open_clip_pytorch_model.bin','resize':(224,224)}, #512\n", "                'ViT-L-14':{'pretrained':'/opt/data/private/lq/huggingface/hub/models--laion--CLIP-ViT-L-14-laion2B-s32B-b82K/snapshots/1627032197142fbe2a7cfec626f4ced3ae60d07a/open_clip_pytorch_model.bin','resize':(224,224)}, #768\n", "                'ViT-H-14':{'pretrained':'/opt/data/private/lq/huggingface/hub/models--laion--CLIP-ViT-H-14-laion2B-s32B-b79K/snapshots/de081ac0a0ca8dc9d1533eed1ae884bb8ae1404b/open_clip_pytorch_model.bin','resize':(224,224)}, #1024\n", "                'ViT-g-14':{'pretrained':'/opt/data/private/lq/huggingface/hub/models--laion--CLIP-ViT-g-14-laion2B-s34B-b88K/snapshots/15efd0f6ac0c40c0f9da7becca03c974d7012604/open_clip_pytorch_model.bin','resize':(224,224)}, #1024\n", "                'ViT-bigG-14':{'pretrained':'/opt/data/private/lq/huggingface/hub/models--laion--CLIP-ViT-bigG-14-laion2B-39B-b160k/snapshots/bc7788f151930d91b58474715fdce5524ad9a189/open_clip_pytorch_model.bin','resize':(224,224)}, #1280\n", "            }\n", "\n", "model_type = 'ViT-H-14'\n", "import open_clip\n", "vlmodel, preprocess,_ = open_clip.create_model_and_transforms(model_type, device=f\"{device}\",pretrained=pretrain_map[model_type]['pretrained'])\n", "for param in vlmodel.parameters():\n", "    param.requires_grad = False\n", "vlmodel.eval()\n", "\n", "test_caption1 = \"A man with a red helmet on a small moped on a dirt road.\"\n", "test_caption2 = \"A young girl inhales with the intent of blowing out a candle.\"\n", "test_caption3 = \"A man on a bicycle riding next to a train.\"\n", "test_caption4 = \"A kitchen is shown with a variety of items on the counters.\"\n", "test_caption5 = \"A wooden ball on top of a wooden stick.\"\n", "\n", "# 提取所有对应的openclip特征\n", "test_captions = [test_caption1, test_caption2, test_caption3, test_caption4, test_caption5]\n", "clip_features = []\n", "\n", "# 逐个处理每个caption以便于跟踪\n", "for caption in test_captions:\n", "    # 提取特征\n", "    tokens = open_clip.tokenize([caption]).to(device)\n", "    with torch.no_grad():\n", "        feature = vlmodel.encode_text(tokens).float()\n", "        # 归一化特征\n", "        feature = feature / feature.norm(dim=-1, keepdim=True)\n", "        clip_features.append(feature.cpu().numpy())\n", "\n", "# 将特征堆叠为一个数组\n", "clip_features = np.vstack(clip_features)\n", "print(f\"Features shape: {clip_features.shape}\")\n", "\n", "# 保存特征到npy文件\n", "np.save('clip_coco_test.npy', clip_features)\n", "print(\"Features saved to clip_coco_test.npy\")\n", "\n", "print(\"Original captions:\")\n", "for i, caption in enumerate(test_captions):\n", "    print(f\"{i+1}. {caption}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}