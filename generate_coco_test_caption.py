import os
import torch
import numpy as np
import open_clip
from tqdm import tqdm
import json
import pickle
from transformers import GPT2LMHeadModel

# 设置设备
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"Using device: {device}")

# 加载OpenCLIP模型
model_name = "ViT-H-14"  # 与训练时使用的模型一致
clip_model, _, preprocess = open_clip.create_model_and_transforms(model_name, device=device)
tokenizer = open_clip.get_tokenizer(model_name)

# 加载DeCap模型
class MLP(torch.nn.Module):
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.model(x)

    def __init__(self, sizes: tuple, bias=True, act=torch.nn.Tanh):
        super(MLP, self).__init__()
        layers = []
        for i in range(len(sizes) - 1):
            layers.append(torch.nn.Linear(sizes[i], sizes[i + 1], bias=bias))
            if i < len(sizes) - 2:
                layers.append(act())
        self.model = torch.nn.Sequential(*layers)

class DeCap(torch.nn.Module):
    def __init__(self, prefix_size: int = 512):
        super(DeCap, self).__init__()
        # 加载解码器配置
        with open('./decoder_config.pkl', 'rb') as f:
            config = pickle.load(f)
        self.decoder = GPT2LMHeadModel(config)
        self.embedding_size = self.decoder.transformer.wte.weight.shape[1]
        self.clip_project = MLP((prefix_size, self.embedding_size))
        
    def forward(self, clip_features, tokens):
        embedding_text = self.decoder.transformer.wte(tokens)
        embedding_clip = self.clip_project(clip_features)
        embedding_clip = embedding_clip.reshape(-1, 1, self.embedding_size)
        embedding_cat = torch.cat([embedding_clip, embedding_text], dim=1)
        out = self.decoder(inputs_embeds=embedding_cat)
        return out

# 解码函数
def Decoding(model, clip_features):
    model.eval()
    embedding_cat = model.clip_project(clip_features).reshape(1, 1, -1)
    entry_length = 30
    temperature = 1
    tokens = None
    for i in range(entry_length):
        outputs = model.decoder(inputs_embeds=embedding_cat)
        logits = outputs.logits
        logits = logits[:, -1, :] / (temperature if temperature > 0 else 1.0)
        logits = torch.nn.functional.softmax(logits, dim=-1)
        next_token = torch.argmax(logits, -1).unsqueeze(0)
        
        if tokens is None:
            tokens = next_token
        else:
            tokens = torch.cat((tokens, next_token), dim=1)
        if next_token.item() == 49407:  # End of text token
            break
            
        next_token_embed = model.decoder.transformer.wte(next_token)
        embedding_cat = torch.cat((embedding_cat, next_token_embed), dim=1)
    
    try:
        output_list = list(tokens.squeeze().cpu().numpy())
        # 使用OpenCLIP的tokenizer解码
        output = tokenizer.decode(output_list)
        output = output.replace('<|startoftext|>', '').replace('<|endoftext|>', '')
    except:
        output = 'None'
    return output

def main():
    # 原始测试句子
    test_caption1 = "A man with a red helmet on a small moped on a dirt road."
    test_caption2 = "A young girl inhales with the intent of blowing out a candle."
    test_caption3 = "A man on a bicycle riding next to a train."
    test_caption4 = "A kitchen is shown with a variety of items on the counters."
    test_caption5 = "A wooden ball on top of a wooden stick."
    
    test_captions = [test_caption1, test_caption2, test_caption3, test_caption4, test_caption5]
    
    # 加载预训练的DeCap模型
    model = DeCap(prefix_size=1024)  # 确保prefix_size与训练时一致，ViT-H-14是1024维
    weights_path = './coco_prefix-009.pt'
    model.load_state_dict(torch.load(weights_path, map_location=torch.device(device)))
    model = model.to(device)
    model.eval()
    
    # 加载CLIP文本特征
    clip_features = np.load('clip_coco_test.npy')
    print(f"Loaded {len(clip_features)} CLIP text features")
    
    # 生成句子并与原始句子对比
    print("\n原始句子与解码句子对比:")
    print("-" * 80)
    
    generated_captions = []
    for i, feature in enumerate(clip_features):
        # 转换为tensor并规范化
        feature_tensor = torch.tensor(feature, dtype=torch.float32).to(device)
        feature_tensor = feature_tensor.unsqueeze(0)  # 添加批次维度
        
        # 生成文本
        caption = Decoding(model, feature_tensor)
        generated_captions.append(caption)
        
        # 打印对比
        print(f"原始 [{i+1}]: {test_captions[i]}")
        print(f"解码 [{i+1}]: {caption}")
        print("-" * 80)
    
    # 保存生成的句子
    with open('generated_coco_test_captions.json', 'w') as f:
        json.dump(generated_captions, f)
    
    print(f"Generated and saved {len(generated_captions)} captions to generated_coco_test_captions.json")

if __name__ == "__main__":
    main()