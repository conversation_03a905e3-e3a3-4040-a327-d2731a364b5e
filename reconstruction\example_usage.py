#!/usr/bin/env python3
"""
示例：如何使用修改后的 recon_custom.py 进行图像生成和指标计算

使用说明：
1. 确保已安装必要的依赖包
2. 准备好输入数据（EEG特征文件）
3. 可选：准备参考图像用于指标计算
4. 运行脚本生成图像并计算指标
"""

import os
import sys
import torch
import numpy as np
from tqdm import tqdm

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_environment():
    """设置运行环境"""
    # 设置设备
    device = 'cuda:1' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 设置随机种子
    torch.manual_seed(1024)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(1024)
    
    return device

def prepare_data(data_path, max_samples=None):
    """准备输入数据"""
    print(f"加载数据: {data_path}")
    
    if not os.path.exists(data_path):
        raise FileNotFoundError(f"数据文件不存在: {data_path}")
    
    # 加载EEG特征
    h = np.load(data_path)
    
    if max_samples is not None:
        h = h[:max_samples]
    
    h = torch.from_numpy(h)
    print(f"数据形状: {h.shape}")
    
    return h

def generate_images(h, output_dir, device):
    """生成图像"""
    from recon_custom import Generator4Embeds
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")
    
    # 创建生成器
    gen = torch.Generator(device=device)
    gen.manual_seed(1024)
    
    generator = Generator4Embeds(
        num_inference_steps=5, 
        device=device, 
        img2img_strength=0.0,
        ip_adapter_scale=1
    )
    
    # 生成图像
    print("开始生成图像...")
    for i in tqdm(range(h.shape[0]), desc="生成图像"):
        # 提取单个样本，保持batch维度
        h_single = h[i:i+1]
        
        # 生成图像
        reconstructed_image = generator.generate(
            h_single.to(dtype=torch.float16), 
            generator=gen
        )
        
        # 保存图像
        output_path = os.path.join(output_dir, f'generated{i:04d}.png')
        reconstructed_image.save(output_path)
        
        if (i + 1) % 100 == 0:
            print(f'已生成 {i + 1} 张图像')
    
    print(f"图像生成完成，共生成 {h.shape[0]} 张图像")

def calculate_metrics(output_dir, device):
    """计算图像指标"""
    from recon_custom import cal_metrics
    
    print("开始计算图像指标...")
    cal_metrics(output_dir, device)

def main():
    """主函数"""
    # 配置参数
    config = {
        'data_path': 'evaluation_metrics/brain2clip/eeg2image_sub1&2.npy',
        'output_dir': './test/midasnew/EEG',
        'max_samples': 100,  # 限制样本数量用于测试，设为None使用全部数据
    }
    
    try:
        # 设置环境
        device = setup_environment()
        
        # 准备数据
        h = prepare_data(config['data_path'], config['max_samples'])
        
        # 生成图像
        generate_images(h, config['output_dir'], device)
        
        # 计算指标
        calculate_metrics(config['output_dir'], device)
        
        print("处理完成！")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
