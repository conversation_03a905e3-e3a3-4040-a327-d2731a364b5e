#!/usr/bin/env python3
"""
使用修改后的recon_custom.py进行图像生成和指标计算的简单示例
"""

import torch
import os
import sys

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from recon_custom import generate_images_and_calculate_metrics

def main():
    """主函数"""
    
    # 基本配置
    config = {
        'data_path': 'evaluation_metrics/brain2clip/eeg2image_sub1&2.npy',
        'output_dir': './test/midasnew/EEG_simple',
        'reference_images_path': None,  # 使用虚拟参考图像
        'max_samples': 50,  # 仅处理50个样本用于快速测试
        'device': 'cuda:1' if torch.cuda.is_available() else 'cpu',
        'num_inference_steps': 5,
        'img2img_strength': 0.0,
        'ip_adapter_scale': 1
    }
    
    print("配置参数:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    print()
    
    try:
        # 运行图像生成和指标计算
        results = generate_images_and_calculate_metrics(**config)
        
        print("\n" + "="*50)
        print("处理完成！")
        print("="*50)
        
        if results:
            print("\n指标计算结果:")
            for metric, value in results.items():
                if value is not None:
                    if isinstance(value, float):
                        print(f"  {metric}: {value:.4f}")
                    else:
                        print(f"  {metric}: {value}")
                else:
                    print(f"  {metric}: N/A (计算失败)")
        else:
            print("指标计算失败")
            
        print(f"\n结果文件保存在: {config['output_dir']}")
        print("包含以下文件:")
        print("  - generated*.png: 生成的图像（PNG格式，用于查看）")
        print("  - ??????_rec.pt: 生成的图像（tensor格式，用于指标计算）")
        print("  - ??????_img.pt: 参考图像（tensor格式，用于指标计算）")
        print("  - _metrics_on_*samples.csv: 指标计算结果")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
