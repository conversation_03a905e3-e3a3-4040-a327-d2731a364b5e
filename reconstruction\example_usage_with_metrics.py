#!/usr/bin/env python3
"""
使用修改后的recon_custom.py进行图像生成和指标计算的示例
"""

import torch
import os
import numpy as np
from tqdm import tqdm
import torchvision.transforms as transforms
from recon_custom import Generator4Embeds, cal_metrics, save_tensor_images, create_dummy_reference_images, load_reference_images

def setup_environment():
    """设置环境和设备"""
    # 禁用某些CUDA优化以避免兼容性问题
    torch.backends.cuda.enable_flash_sdp(False)
    torch.backends.cuda.enable_mem_efficient_sdp(False)
    
    # 设置设备
    device = 'cuda:1' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    return device

def prepare_data(data_path, max_samples=None):
    """准备输入数据"""
    print(f"加载数据: {data_path}")
    
    # 加载EEG到图像的嵌入数据
    h = np.load(data_path)
    
    if max_samples:
        h = h[:max_samples]
    
    h = torch.from_numpy(h)
    print(f"数据形状: {h.shape}")
    
    return h

def generate_images_with_metrics(h, output_dir, device, reference_images_path=None):
    """生成图像并计算指标"""
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置随机种子
    gen = torch.Generator(device=device)
    gen.manual_seed(1024)
    
    # 创建生成器实例
    print("初始化图像生成器...")
    generator = Generator4Embeds(
        num_inference_steps=5, 
        device=device, 
        img2img_strength=0.0,
        ip_adapter_scale=1
    )
    
    # 批量生成图像
    print("开始生成图像...")
    all_generated_images = []
    
    for i in tqdm(range(h.shape[0]), desc="Generating images"):
        # 提取单个样本，保持 batch 维度
        h_single = h[i:i+1]  # 形状从 (N, ...) 变为 (1, ...)
        reconstructed_image = generator.generate(h_single.to(dtype=torch.float16), generator=gen)
        
        # 将PIL图像转换为tensor
        transform_to_tensor = transforms.Compose([
            transforms.ToTensor(),
        ])
        
        img_tensor = transform_to_tensor(reconstructed_image)
        all_generated_images.append(img_tensor)
        
        # 保存PNG格式用于查看
        png_output_path = os.path.join(output_dir, f'generated{i}.png')
        reconstructed_image.save(png_output_path)
    
    # 将生成的图像保存为.pt格式，兼容eval.py
    generated_tensors = torch.stack(all_generated_images)
    save_tensor_images(generated_tensors, output_dir, prefix="rec", start_idx=0)
    
    # 处理参考图像
    if reference_images_path and os.path.exists(reference_images_path):
        print("加载参考图像...")
        reference_tensors = load_reference_images(reference_images_path, max_images=h.shape[0])
        if reference_tensors is not None:
            save_tensor_images(reference_tensors, output_dir, prefix="img", start_idx=0)
            print("参考图像已保存为.pt格式")
        else:
            print("未找到有效的参考图像，创建虚拟参考图像...")
            create_dummy_reference_images(h.shape[0], output_dir)
    else:
        print("创建虚拟参考图像用于指标计算...")
        create_dummy_reference_images(h.shape[0], output_dir)
    
    print(f"图像生成完成，共生成 {h.shape[0]} 张图像")
    
    # 计算指标
    print("开始计算图像指标...")
    results = cal_metrics(output_dir, device)
    
    return results

def main():
    """主函数"""
    # 配置参数
    config = {
        'data_path': 'evaluation_metrics/brain2clip/eeg2image_sub1&2.npy',
        'output_dir': './test/midasnew/EEG_with_metrics',
        'reference_images_path': None,  # 设置为参考图像路径，或None使用虚拟参考图像
        'max_samples': 100,  # 限制样本数量用于测试，设为None使用全部数据
    }
    
    try:
        # 设置环境
        device = setup_environment()
        
        # 准备数据
        h = prepare_data(config['data_path'], config['max_samples'])
        
        # 生成图像并计算指标
        results = generate_images_with_metrics(
            h, 
            config['output_dir'], 
            device, 
            config['reference_images_path']
        )
        
        print("\n=== 处理完成 ===")
        if results:
            print("指标计算结果:")
            for metric, value in results.items():
                if value is not None:
                    print(f"  {metric}: {value:.4f}")
                else:
                    print(f"  {metric}: N/A")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
