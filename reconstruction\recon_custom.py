import torch
torch.backends.cuda.enable_flash_sdp(False)  # 禁用 Flash Attention
torch.backends.cuda.enable_mem_efficient_sdp(False)  # 禁用内存高效 SDP
# torch.backends.cuda.enable_math_sdp(True)  # 启用标准数学实现
from diffusers.pipelines.stable_diffusion_xl.pipeline_stable_diffusion_xl import *
import PIL
import os
import numpy as np
from tqdm import tqdm
import pandas as pd
import scipy as sp
from PIL import Image

from torchvision import transforms
from torchvision.models.feature_extraction import create_feature_extractor

import sys
sys.path.append("/root/workspace/multi_vae")
import data

# 添加数据集加载函数
def load_nsd_dataset(data_path, subj, batch_size=32, num_workers=4, mode='test', pool_type=None, pool_num=None, length=None, use_mean_feature=True):
    """
    加载NSD数据集
    
    Args:
        data_path: 数据路径
        subj: 被试ID
        batch_size: 批次大小
        num_workers: 数据加载线程数
        mode: 'train' 或 'test'
        pool_type: 池化类型
        pool_num: 池化数量
        length: 数据长度限制
        use_mean_feature: 是否使用平均特征
        
    Returns:
        dataloader: 数据加载器
    """
    print(f"Loading NSD dataset for subject {subj}, mode: {mode}")
    
    dataset = data.NSDDataset(
        f"{data_path}/webdataset_avg_new/{mode}/subj0{subj}",
        extensions=['nsdgeneral.npy', "jpg", "subj"],
        pool_type=pool_type,
        pool_num=pool_num,
        length=length,
        use_mean_feature=use_mean_feature,
    )
    
    dataloader = torch.utils.data.DataLoader(
        dataset, 
        batch_size=batch_size, 
        num_workers=num_workers, 
        pin_memory=True
    )
    
    return dataloader

def load_eeg_dataset(subj, mode='test', batch_size=32, num_workers=4, eeg_size=None, use_mean_feature=False):
    """
    加载EEG数据集
    
    Args:
        subj: 被试ID
        mode: 'train' 或 'test'
        batch_size: 批次大小
        num_workers: 数据加载线程数
        eeg_size: EEG数据大小
        use_mean_feature: 是否使用平均特征
        
    Returns:
        dataloader: 数据加载器
    """
    print(f"Loading EEG dataset for subject {subj}, mode: {mode}")
    
    dataset = data.NSDEEGDataset(
        subj,
        mode,
        eeg_size=eeg_size,
        use_mean_feature=use_mean_feature,
    )
    
    dataloader = torch.utils.data.DataLoader(
        dataset, 
        batch_size=batch_size, 
        num_workers=num_workers, 
        pin_memory=True
    )
    
    return dataloader

def extract_reference_images_from_dataset(dataloaders, output_dir=None, max_samples=None):
    """
    从数据集中提取参考图像
    
    Args:
        dataloaders: 数据加载器列表
        output_dir: 输出目录，如果提供则保存图像
        max_samples: 最大样本数
        
    Returns:
        reference_images: 参考图像列表
    """
    print("Extracting reference images from dataset...")
    
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    reference_images = []
    sample_count = 0
    
    for i, data_batch in enumerate(dataloaders[0]):
        # for _, data_batch in enumerate(tqdm(dataloader, desc=f"Processing dataloader {i+1}/{len(dataloaders)}")):
            # 提取图像数据
            if 'jpg' in data_batch:
                images = data_batch['jpg']
                
                for j, img_tensor in enumerate(images):

                    # 转换为PIL图像用于显示或保存
                    # img_np = img_tensor.cpu().numpy().transpose(1, 2, 0)
                    # img_np = np.clip(img_np, 0, 1)
                    # img_pil = Image.fromarray((img_np * 255).astype(np.uint8))
                    # png_output_path = os.path.join(output_dir, f'r_{i}.png')
                    # img_pil.save(png_output_path)

                    reference_images.append(img_tensor)
                    
                    # 保存图像
                    if output_dir:
                        # 同时保存为pt格式用于指标计算
                        pt_path = os.path.join(output_dir, f'{i}_img.pt')
                        torch.save(img_tensor.unsqueeze(0), pt_path)
                    
                    sample_count += 1
                    
                    # 检查是否达到最大样本数
                    if max_samples and sample_count >= max_samples:
                        print(f"Reached maximum sample count: {max_samples}")
                        return reference_images
    
    print(f"Extracted {len(reference_images)} reference images")
    return reference_images

@torch.no_grad()
@replace_example_docstring(EXAMPLE_DOC_STRING)
def generate_ip_adapter_embeds(
    self,
    prompt: Union[str, List[str]] = None,
    prompt_2: Optional[Union[str, List[str]]] = None,
    height: Optional[int] = None,
    width: Optional[int] = None,
    num_inference_steps: int = 50,
    timesteps: List[int] = None,
    denoising_end: Optional[float] = None,
    guidance_scale: float = 5.0,
    img2img_strength: float = 0.85,  # 新增参数
    low_level_image: Optional[PipelineImageInput] = None,  # 新增参数
    negative_prompt: Optional[Union[str, List[str]]] = None,
    negative_prompt_2: Optional[Union[str, List[str]]] = None,
    num_images_per_prompt: Optional[int] = 1,
    eta: float = 0.0,
    generator: Optional[Union[torch.Generator, List[torch.Generator]]] = None,
    low_level_latent: Optional[torch.FloatTensor] = None,
    prompt_embeds: Optional[torch.FloatTensor] = None,
    negative_prompt_embeds: Optional[torch.FloatTensor] = None,
    pooled_prompt_embeds: Optional[torch.FloatTensor] = None,
    negative_pooled_prompt_embeds: Optional[torch.FloatTensor] = None,
    ip_adapter_image: Optional[PipelineImageInput] = None,
    ip_adapter_embeds: Optional[torch.FloatTensor] = None,
    output_type: Optional[str] = "pil",
    return_dict: bool = True,
    cross_attention_kwargs: Optional[Dict[str, Any]] = None,
    guidance_rescale: float = 0.0,
    original_size: Optional[Tuple[int, int]] = None,
    crops_coords_top_left: Tuple[int, int] = (0, 0),
    target_size: Optional[Tuple[int, int]] = None,
    negative_original_size: Optional[Tuple[int, int]] = None,
    negative_crops_coords_top_left: Tuple[int, int] = (0, 0),
    negative_target_size: Optional[Tuple[int, int]] = None,
    clip_skip: Optional[int] = None,
    callback_on_step_end: Optional[Callable[[int, int, Dict], None]] = None,
    callback_on_step_end_tensor_inputs: List[str] = ["latents"],
    **kwargs,
):
    r"""
    Function invoked when calling the pipeline for generation.

    Args:
        prompt (`str` or `List[str]`, *optional*):
            The prompt or prompts to guide the image generation. If not defined, one has to pass `prompt_embeds`.
            instead.
        prompt_2 (`str` or `List[str]`, *optional*):
            The prompt or prompts to be sent to the `tokenizer_2` and `text_encoder_2`. If not defined, `prompt` is
            used in both text-encoders
        height (`int`, *optional*, defaults to self.unet.config.sample_size * self.vae_scale_factor):
            The height in pixels of the generated image. This is set to 1024 by default for the best results.
            Anything below 512 pixels won't work well for
            [stabilityai/stable-diffusion-xl-base-1.0](https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0)
            and checkpoints that are not specifically fine-tuned on low resolutions.
        width (`int`, *optional*, defaults to self.unet.config.sample_size * self.vae_scale_factor):
            The width in pixels of the generated image. This is set to 1024 by default for the best results.
            Anything below 512 pixels won't work well for
            [stabilityai/stable-diffusion-xl-base-1.0](https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0)
            and checkpoints that are not specifically fine-tuned on low resolutions.
        num_inference_steps (`int`, *optional*, defaults to 50):
            The number of denoising steps. More denoising steps usually lead to a higher quality image at the
            expense of slower inference.
        timesteps (`List[int]`, *optional*):
            Custom timesteps to use for the denoising process with schedulers which support a `timesteps` argument
            in their `set_timesteps` method. If not defined, the default behavior when `num_inference_steps` is
            passed will be used. Must be in descending order.
        denoising_end (`float`, *optional*):
            When specified, determines the fraction (between 0.0 and 1.0) of the total denoising process to be
            completed before it is intentionally prematurely terminated. As a result, the returned sample will
            still retain a substantial amount of noise as determined by the discrete timesteps selected by the
            scheduler. The denoising_end parameter should ideally be utilized when this pipeline forms a part of a
            "Mixture of Denoisers" multi-pipeline setup, as elaborated in [**Refining the Image
            Output**](https://huggingface.co/docs/diffusers/api/pipelines/stable_diffusion/stable_diffusion_xl#refining-the-image-output)
        guidance_scale (`float`, *optional*, defaults to 5.0):
            Guidance scale as defined in [Classifier-Free Diffusion Guidance](https://arxiv.org/abs/2207.12598).
            `guidance_scale` is defined as `w` of equation 2. of [Imagen
            Paper](https://arxiv.org/pdf/2205.11487.pdf). Guidance scale is enabled by setting `guidance_scale >
            1`. Higher guidance scale encourages to generate images that are closely linked to the text `prompt`,
            usually at the expense of lower image quality.
        negative_prompt (`str` or `List[str]`, *optional*):
            The prompt or prompts not to guide the image generation. If not defined, one has to pass
            `negative_prompt_embeds` instead. Ignored when not using guidance (i.e., ignored if `guidance_scale` is
            less than `1`).
        negative_prompt_2 (`str` or `List[str]`, *optional*):
            The prompt or prompts not to guide the image generation to be sent to `tokenizer_2` and
            `text_encoder_2`. If not defined, `negative_prompt` is used in both text-encoders
        num_images_per_prompt (`int`, *optional*, defaults to 1):
            The number of images to generate per prompt.
        eta (`float`, *optional*, defaults to 0.0):
            Corresponds to parameter eta (η) in the DDIM paper: https://arxiv.org/abs/2010.02502. Only applies to
            [`schedulers.DDIMScheduler`], will be ignored for others.
        generator (`torch.Generator` or `List[torch.Generator]`, *optional*):
            One or a list of [torch generator(s)](https://pytorch.org/docs/stable/generated/torch.Generator.html)
            to make generation deterministic.
        low_level_latent (`torch.FloatTensor`, *optional*):
            Pre-generated noisy low_level_latent, sampled from a Gaussian distribution, to be used as inputs for image
            generation. Can be used to tweak the same generation with different prompts. If not provided, a latents
            tensor will ge generated by sampling using the supplied random `generator`.
        prompt_embeds (`torch.FloatTensor`, *optional*):
            Pre-generated text embeddings. Can be used to easily tweak text inputs, *e.g.* prompt weighting. If not
            provided, text embeddings will be generated from `prompt` input argument.
        negative_prompt_embeds (`torch.FloatTensor`, *optional*):
            Pre-generated negative text embeddings. Can be used to easily tweak text inputs, *e.g.* prompt
            weighting. If not provided, negative_prompt_embeds will be generated from `negative_prompt` input
            argument.
        pooled_prompt_embeds (`torch.FloatTensor`, *optional*):
            Pre-generated pooled text embeddings. Can be used to easily tweak text inputs, *e.g.* prompt weighting.
            If not provided, pooled text embeddings will be generated from `prompt` input argument.
        negative_pooled_prompt_embeds (`torch.FloatTensor`, *optional*):
            Pre-generated negative pooled text embeddings. Can be used to easily tweak text inputs, *e.g.* prompt
            weighting. If not provided, pooled negative_prompt_embeds will be generated from `negative_prompt`
            input argument.
        ip_adapter_image: (`PipelineImageInput`, *optional*): Optional image input to work with IP Adapters.
        ip_adapter_embeds: (`FloatTensor`, *optional*): Optional image embeddings to work with IP Adapters.
        output_type (`str`, *optional*, defaults to `"pil"`):
            The output format of the generate image. Choose between
            [PIL](https://pillow.readthedocs.io/en/stable/): `PIL.Image.Image` or `np.array`.
        return_dict (`bool`, *optional*, defaults to `True`):
            Whether or not to return a [`~pipelines.stable_diffusion_xl.StableDiffusionXLPipelineOutput`] instead
            of a plain tuple.
        cross_attention_kwargs (`dict`, *optional*):
            A kwargs dictionary that if specified is passed along to the `AttentionProcessor` as defined under
            `self.processor` in
            [diffusers.models.attention_processor](https://github.com/huggingface/diffusers/blob/main/src/diffusers/models/attention_processor.py).
        guidance_rescale (`float`, *optional*, defaults to 0.0):
            Guidance rescale factor proposed by [Common Diffusion Noise Schedules and Sample Steps are
            Flawed](https://arxiv.org/pdf/2305.08891.pdf) `guidance_scale` is defined as `φ` in equation 16. of
            [Common Diffusion Noise Schedules and Sample Steps are Flawed](https://arxiv.org/pdf/2305.08891.pdf).
            Guidance rescale factor should fix overexposure when using zero terminal SNR.
        original_size (`Tuple[int]`, *optional*, defaults to (1024, 1024)):
            If `original_size` is not the same as `target_size` the image will appear to be down- or upsampled.
            `original_size` defaults to `(height, width)` if not specified. Part of SDXL's micro-conditioning as
            explained in section 2.2 of
            [https://huggingface.co/papers/2307.01952](https://huggingface.co/papers/2307.01952).
        crops_coords_top_left (`Tuple[int]`, *optional*, defaults to (0, 0)):
            `crops_coords_top_left` can be used to generate an image that appears to be "cropped" from the position
            `crops_coords_top_left` downwards. Favorable, well-centered images are usually achieved by setting
            `crops_coords_top_left` to (0, 0). Part of SDXL's micro-conditioning as explained in section 2.2 of
            [https://huggingface.co/papers/2307.01952](https://huggingface.co/papers/2307.01952).
        target_size (`Tuple[int]`, *optional*, defaults to (1024, 1024)):
            For most cases, `target_size` should be set to the desired height and width of the generated image. If
            not specified it will default to `(height, width)`. Part of SDXL's micro-conditioning as explained in
            section 2.2 of [https://huggingface.co/papers/2307.01952](https://huggingface.co/papers/2307.01952).
        negative_original_size (`Tuple[int]`, *optional*, defaults to (1024, 1024)):
            To negatively condition the generation process based on a specific image resolution. Part of SDXL's
            micro-conditioning as explained in section 2.2 of
            [https://huggingface.co/papers/2307.01952](https://huggingface.co/papers/2307.01952). For more
            information, refer to this issue thread: https://github.com/huggingface/diffusers/issues/4208.
        negative_crops_coords_top_left (`Tuple[int]`, *optional*, defaults to (0, 0)):
            To negatively condition the generation process based on a specific crop coordinates. Part of SDXL's
            micro-conditioning as explained in section 2.2 of
            [https://huggingface.co/papers/2307.01952](https://huggingface.co/papers/2307.01952). For more
            information, refer to this issue thread: https://github.com/huggingface/diffusers/issues/4208.
        negative_target_size (`Tuple[int]`, *optional*, defaults to (1024, 1024)):
            To negatively condition the generation process based on a target image resolution. It should be as same
            as the `target_size` for most cases. Part of SDXL's micro-conditioning as explained in section 2.2 of
            [https://huggingface.co/papers/2307.01952](https://huggingface.co/papers/2307.01952). For more
            information, refer to this issue thread: https://github.com/huggingface/diffusers/issues/4208.
        callback_on_step_end (`Callable`, *optional*):
            A function that calls at the end of each denoising steps during the inference. The function is called
            with the following arguments: `callback_on_step_end(self: DiffusionPipeline, step: int, timestep: int,
            callback_kwargs: Dict)`. `callback_kwargs` will include a list of all tensors as specified by
            `callback_on_step_end_tensor_inputs`.
        callback_on_step_end_tensor_inputs (`List`, *optional*):
            The list of tensor inputs for the `callback_on_step_end` function. The tensors specified in the list
            will be passed as `callback_kwargs` argument. You will only be able to include variables listed in the
            `._callback_tensor_inputs` attribute of your pipeline class.

    Examples:

    Returns:
        [`~pipelines.stable_diffusion_xl.StableDiffusionXLPipelineOutput`] or `tuple`:
        [`~pipelines.stable_diffusion_xl.StableDiffusionXLPipelineOutput`] if `return_dict` is True, otherwise a
        `tuple`. When returning a tuple, the first element is a list with the generated images.
    """

    callback = kwargs.pop("callback", None)
    callback_steps = kwargs.pop("callback_steps", None)

    if callback is not None:
        deprecate(
            "callback",
            "1.0.0",
            "Passing `callback` as an input argument to `__call__` is deprecated, consider use `callback_on_step_end`",
        )
    if callback_steps is not None:
        deprecate(
            "callback_steps",
            "1.0.0",
            "Passing `callback_steps` as an input argument to `__call__` is deprecated, consider use `callback_on_step_end`",
        )
        

        
    # 0. Default height and width to unet
    height = height or self.default_sample_size * self.vae_scale_factor
    width = width or self.default_sample_size * self.vae_scale_factor

    original_size = original_size or (height, width)
    target_size = target_size or (height, width)

    # 1. Check inputs. Raise error if not correct
    self.check_inputs(
        prompt,
        prompt_2,
        height,
        width,
        callback_steps,
        negative_prompt,
        negative_prompt_2,
        prompt_embeds,
        negative_prompt_embeds,
        pooled_prompt_embeds,
        negative_pooled_prompt_embeds,
        callback_on_step_end_tensor_inputs,
    )

    self._guidance_scale = guidance_scale
    self._guidance_rescale = guidance_rescale
    self._clip_skip = clip_skip
    self._cross_attention_kwargs = cross_attention_kwargs
    self._denoising_end = denoising_end

    # 2. Define call parameters
    if prompt is not None and isinstance(prompt, str):
        batch_size = 1
    elif prompt is not None and isinstance(prompt, list):
        batch_size = len(prompt)
    else:
        batch_size = prompt_embeds.shape[0]

    device = self._execution_device


    # 新增：处理 low_level_image
    if low_level_image is not None:
        # 确保 low_level_image 已经被处理为适当的格式
        if isinstance(low_level_image, PIL.Image.Image):
            low_level_image = self.image_processor.preprocess(low_level_image)
        elif isinstance(low_level_image, torch.Tensor):
            low_level_image = low_level_image.to(device)
        else:
            raise ValueError("low_level_image should be a PIL image or a torch tensor.")
        
    # 3. Encode input prompt
    lora_scale = (
        self.cross_attention_kwargs.get("scale", None) if self.cross_attention_kwargs is not None else None
    )

    (
        prompt_embeds,
        negative_prompt_embeds,
        pooled_prompt_embeds,
        negative_pooled_prompt_embeds,
    ) = self.encode_prompt(
        prompt=prompt,
        prompt_2=prompt_2,
        device=device,
        num_images_per_prompt=num_images_per_prompt,
        do_classifier_free_guidance=self.do_classifier_free_guidance,
        negative_prompt=negative_prompt,
        negative_prompt_2=negative_prompt_2,
        prompt_embeds=prompt_embeds,
        negative_prompt_embeds=negative_prompt_embeds,
        pooled_prompt_embeds=pooled_prompt_embeds,
        negative_pooled_prompt_embeds=negative_pooled_prompt_embeds,
        lora_scale=lora_scale,
        clip_skip=self.clip_skip,
    )

    # 4. Prepare timesteps
    timesteps, num_inference_steps = retrieve_timesteps(self.scheduler, num_inference_steps, device, timesteps)
    
    # 新增：根据 img2img_strength 计算初始步数
    if low_level_image is not None:
        # 根据 img2img_strength 计算需要跳过的时间步数
        init_timestep = min(int(num_inference_steps * img2img_strength), num_inference_steps)
        t_start = max(num_inference_steps - init_timestep, 0)
        timesteps = timesteps[t_start:]
    else:
        t_start = 0  # 如果没有 low_level_image，从头开始
        
    # 5. Prepare latent variables
    num_channels_latents = self.unet.config.in_channels
    # latents = self.prepare_latents(
    #     batch_size * num_images_per_prompt,
    #     num_channels_latents,
    #     height,
    #     width,
    #     prompt_embeds.dtype,
    #     device,
    #     generator,
    # )
    
    # 新增：准备初始 latents
    if low_level_image is not None and low_level_latent is None:
        # 编码 low_level_image 到潜在空间
        latents = self.prepare_latents_img2img(
            low_level_image,
            batch_size * num_images_per_prompt,
            num_channels_latents,
            height,
            width,
            prompt_embeds.dtype,
            device,
            generator,
        )
    elif low_level_latent is not None:        
        latents = self.prepare_latents_latent2img(
            low_level_latent,
            batch_size * num_images_per_prompt,
            num_channels_latents,
            height,
            width,
            prompt_embeds.dtype,
            device,
            generator,
        )        
        
    else:
        # 如果没有 low_level_image，随机初始化 latents
        latents = self.prepare_latents(
            batch_size * num_images_per_prompt,
            num_channels_latents,
            height,
            width,
            prompt_embeds.dtype,
            device,
            generator,
        )
        
    # 6. Prepare extra step kwargs. TODO: Logic should ideally just be moved out of the pipeline
    extra_step_kwargs = self.prepare_extra_step_kwargs(generator, eta)

    # 7. Prepare added time ids & embeddings
    add_text_embeds = pooled_prompt_embeds
    if self.text_encoder_2 is None:
        text_encoder_projection_dim = int(pooled_prompt_embeds.shape[-1])
    else:
        text_encoder_projection_dim = self.text_encoder_2.config.projection_dim

    add_time_ids = self._get_add_time_ids(
        original_size,
        crops_coords_top_left,
        target_size,
        dtype=prompt_embeds.dtype,
        text_encoder_projection_dim=text_encoder_projection_dim,
    )
    if negative_original_size is not None and negative_target_size is not None:
        negative_add_time_ids = self._get_add_time_ids(
            negative_original_size,
            negative_crops_coords_top_left,
            negative_target_size,
            dtype=prompt_embeds.dtype,
            text_encoder_projection_dim=text_encoder_projection_dim,
        )
    else:
        negative_add_time_ids = add_time_ids

    if self.do_classifier_free_guidance:
        prompt_embeds = torch.cat([negative_prompt_embeds, prompt_embeds], dim=0)
        add_text_embeds = torch.cat([negative_pooled_prompt_embeds, add_text_embeds], dim=0)
        add_time_ids = torch.cat([negative_add_time_ids, add_time_ids], dim=0)

    prompt_embeds = prompt_embeds.to(device)
    add_text_embeds = add_text_embeds.to(device)
    add_time_ids = add_time_ids.to(device).repeat(batch_size * num_images_per_prompt, 1)

    if ip_adapter_image is not None:
        image_embeds, negative_image_embeds = self.encode_image(ip_adapter_image, device, num_images_per_prompt)
        if self.do_classifier_free_guidance:
            image_embeds = torch.cat([negative_image_embeds, image_embeds])
            image_embeds = image_embeds.to(device)
    
    if ip_adapter_embeds is not None:
        image_embeds = ip_adapter_embeds.to(device=device, dtype=prompt_embeds.dtype)
        if self.do_classifier_free_guidance:
            negative_image_embeds = torch.zeros_like(image_embeds)
            image_embeds = torch.cat([negative_image_embeds, image_embeds])
            image_embeds = image_embeds.to(device)

    # 8. Denoising loop
    num_warmup_steps = max(len(timesteps) - num_inference_steps * self.scheduler.order, 0)

    # 8.1 Apply denoising_end
    if (
        self.denoising_end is not None
        and isinstance(self.denoising_end, float)
        and self.denoising_end > 0
        and self.denoising_end < 1
    ):
        discrete_timestep_cutoff = int(
            round(
                self.scheduler.config.num_train_timesteps
                - (self.denoising_end * self.scheduler.config.num_train_timesteps)
            )
        )
        num_inference_steps = len(list(filter(lambda ts: ts >= discrete_timestep_cutoff, timesteps)))
        timesteps = timesteps[:num_inference_steps]

    # 9. Optionally get Guidance Scale Embedding
    timestep_cond = None
    if self.unet.config.time_cond_proj_dim is not None:
        guidance_scale_tensor = torch.tensor(self.guidance_scale - 1).repeat(batch_size * num_images_per_prompt)
        timestep_cond = self.get_guidance_scale_embedding(
            guidance_scale_tensor, embedding_dim=self.unet.config.time_cond_proj_dim
        ).to(device=device, dtype=latents.dtype)

    self._num_timesteps = len(timesteps)
    with self.progress_bar(total=num_inference_steps) as progress_bar:
        for i, t in enumerate(timesteps):
            # expand the latents if we are doing classifier free guidance
            latent_model_input = torch.cat([latents] * 2) if self.do_classifier_free_guidance else latents

            latent_model_input = self.scheduler.scale_model_input(latent_model_input, t)

            # predict the noise residual
            added_cond_kwargs = {"text_embeds": add_text_embeds, "time_ids": add_time_ids}
            if ip_adapter_image is not None or ip_adapter_embeds is not None:
                added_cond_kwargs["image_embeds"] = image_embeds
            noise_pred = self.unet(
                latent_model_input,
                t,
                encoder_hidden_states=prompt_embeds,
                timestep_cond=timestep_cond,
                cross_attention_kwargs=self.cross_attention_kwargs,
                added_cond_kwargs=added_cond_kwargs,
                return_dict=False,
            )[0]

            # perform guidance
            if self.do_classifier_free_guidance:
                noise_pred_uncond, noise_pred_text = noise_pred.chunk(2)
                noise_pred = noise_pred_uncond + self.guidance_scale * (noise_pred_text - noise_pred_uncond)

            if self.do_classifier_free_guidance and self.guidance_rescale > 0.0:
                # Based on 3.4. in https://arxiv.org/pdf/2305.08891.pdf
                noise_pred = rescale_noise_cfg(noise_pred, noise_pred_text, guidance_rescale=self.guidance_rescale)

            # compute the previous noisy sample x_t -> x_t-1
            latents = self.scheduler.step(noise_pred, t, latents, **extra_step_kwargs, return_dict=False)[0]

            if callback_on_step_end is not None:
                callback_kwargs = {}
                for k in callback_on_step_end_tensor_inputs:
                    callback_kwargs[k] = locals()[k]
                callback_outputs = callback_on_step_end(self, i, t, callback_kwargs)

                latents = callback_outputs.pop("latents", latents)
                prompt_embeds = callback_outputs.pop("prompt_embeds", prompt_embeds)
                negative_prompt_embeds = callback_outputs.pop("negative_prompt_embeds", negative_prompt_embeds)
                add_text_embeds = callback_outputs.pop("add_text_embeds", add_text_embeds)
                negative_pooled_prompt_embeds = callback_outputs.pop(
                    "negative_pooled_prompt_embeds", negative_pooled_prompt_embeds
                )
                add_time_ids = callback_outputs.pop("add_time_ids", add_time_ids)
                negative_add_time_ids = callback_outputs.pop("negative_add_time_ids", negative_add_time_ids)

            # call the callback, if provided
            if i == len(timesteps) - 1 or ((i + 1) > num_warmup_steps and (i + 1) % self.scheduler.order == 0):
                progress_bar.update()
                if callback is not None and i % callback_steps == 0:
                    step_idx = i // getattr(self.scheduler, "order", 1)
                    callback(step_idx, t, latents)

            if XLA_AVAILABLE:
                xm.mark_step()

    if not output_type == "latent":
        # make sure the VAE is in float32 mode, as it overflows in bfloat16
        needs_upcasting = self.vae.dtype == torch.bfloat16 and self.vae.config.force_upcast

        if needs_upcasting:
            self.upcast_vae()
            latents = latents.to(next(iter(self.vae.post_quant_conv.parameters())).dtype)

        image = self.vae.decode(latents / self.vae.config.scaling_factor, return_dict=False)[0]

        # cast back to fp16 if needed
        if needs_upcasting:
            self.vae.to(dtype=torch.bfloat16)
    else:
        image = latents

    if not output_type == "latent":
        # apply watermark if available
        if self.watermark is not None:
            image = self.watermark.apply_watermark(image)

        image = self.image_processor.postprocess(image, output_type=output_type)

    # Offload all models
    self.maybe_free_model_hooks()

    if not return_dict:
        return (image,)

    return StableDiffusionXLPipelineOutput(images=image)

class Generator4Embeds:

    def __init__(self, num_inference_steps=1, device='cuda', img2img_strength=1, ip_adapter_scale=1, low_level_image=None, low_level_latent = None) -> None:

        self.num_inference_steps = num_inference_steps
        self.dtype = torch.bfloat16
        self.device = device
        self.img2img_strength = img2img_strength
        self.low_level_image = low_level_image
        self.low_level_latent = low_level_latent
        # path = '/home/<USER>/.cache/huggingface/hub/models--stabilityai--sdxl-turbo/snapshots/f4b0486b498f84668e828044de1d0c8ba486e05b'
        # path = "/home/<USER>/Workspace/sdxl-turbo/f4b0486b498f84668e828044de1d0c8ba486e05b"
        pipe = DiffusionPipeline.from_pretrained("stabilityai/sdxl-turbo", torch_dtype=torch.bfloat16, variant="fp16")
        # pipe = DiffusionPipeline.from_pretrained(path, torch_dtype=torch.bfloat16, variant="fp16")
        pipe.enable_model_cpu_offload()
        pipe.to(device)
        pipe.generate_ip_adapter_embeds = generate_ip_adapter_embeds.__get__(pipe)
        # load ip adapter
        pipe.load_ip_adapter(
            "h94/IP-Adapter", subfolder="sdxl_models",
            weight_name="ip-adapter_sdxl_vit-h.bin",
            torch_dtype=torch.bfloat16)
        # set ip_adapter scale (defauld is 1)
        pipe.set_ip_adapter_scale(ip_adapter_scale)
        self.pipe = pipe

    def generate(self, image_embeds, text_prompt='', generator=None):
        image_embeds = image_embeds.to(device=self.device, dtype=self.dtype)
        pipe = self.pipe

        # generate image with image prompt - ip_adapter_embeds
        image = pipe.generate_ip_adapter_embeds(
            prompt=text_prompt,
            ip_adapter_embeds=image_embeds,
            num_inference_steps=self.num_inference_steps,
            guidance_scale=0.0,
            generator=generator,
            img2img_strength=self.img2img_strength,
            low_level_image=self.low_level_image,
            low_level_latent=self.low_level_latent,
        ).images[0]

        return image

# output_dir = './test/midasnew/EEG'
# os.makedirs(output_dir, exist_ok=True) 

# device = 'cuda:1'
# # Set seed value
# gen = torch.Generator(device=device)
# gen.manual_seed(1024)
# # Create an instance of the generator
# generator = Generator4Embeds(num_inference_steps=5, device=device, img2img_strength=0.0,
#                                 ip_adapter_scale=1, )

# h = np.load('evaluation_metrics/brain2clip/eeg2image_sub1&2.npy')[:1000, :]
# h = torch.from_numpy(h)
# print(h.shape)

# for i in tqdm(range(h.shape[0]), desc="Generating images"):
#     # 提取单个样本，保持 batch 维度
#     h_single = h[i:i+1]  # 形状从 (1000, ...) 变为 (1, ...)
#     reconstructed_image = generator.generate(h_single.to(dtype=torch.float16), generator=gen)
#     output_path = os.path.join(output_dir, f'generated{i}.png')
#     reconstructed_image.save(output_path)
#     print(f'Image saved to {output_path}\n')


# ==================== 图像指标计算相关函数 ====================

@torch.no_grad()
def two_way_identification(all_brain_recons, all_images, model, preprocess, feature_layer=None, return_avg=True, device='cpu'):
    """二路识别任务实现"""
    preds = model(torch.stack([preprocess(recon) for recon in all_brain_recons], dim=0).to(device))
    reals = model(torch.stack([preprocess(indiv) for indiv in all_images], dim=0).to(device))
    if feature_layer is None:
        preds = preds.float().flatten(1).cpu().numpy()
        reals = reals.float().flatten(1).cpu().numpy()
    else:
        preds = preds[feature_layer].float().flatten(1).cpu().numpy()
        reals = reals[feature_layer].float().flatten(1).cpu().numpy()

    r = np.corrcoef(reals, preds)
    r = r[:len(all_images), len(all_images):]
    congruents = np.diag(r)

    success = r < congruents
    success_cnt = np.sum(success, 0)

    if return_avg:
        perf = np.mean(success_cnt) / (len(all_images)-1)
        return perf
    else:
        return success_cnt, len(all_images)-1


def save_tensor_images(tensor_images, output_dir, prefix="rec", start_idx=0):
    """
    将tensor格式的图像保存为.pt文件，兼容eval.py的文件格式要求

    Args:
        tensor_images: torch.Tensor, 形状为 (N, C, H, W)
        output_dir: str, 输出目录
        prefix: str, 文件前缀 ("rec" 或 "img")
        start_idx: int, 起始索引
    """
    os.makedirs(output_dir, exist_ok=True)

    for i, img_tensor in enumerate(tensor_images):
        # 确保tensor在正确的范围内 [0, 1]
        img_tensor = torch.clamp(img_tensor, 0, 1)

        # 添加batch维度如果需要
        if img_tensor.dim() == 3:
            img_tensor = img_tensor.unsqueeze(0)

        # 保存为.pt文件，使用eval.py期望的命名格式
        filename = f"{start_idx + i:06d}_{prefix}.pt"
        filepath = os.path.join(output_dir, filename)
        torch.save(img_tensor, filepath)


def load_reference_images(reference_path, max_images=None):
    """
    加载参考图像

    Args:
        reference_path: str, 参考图像路径（可以是目录或单个文件）
        max_images: int, 最大加载图像数量

    Returns:
        torch.Tensor or None: 加载的图像tensor
    """
    if not os.path.exists(reference_path):
        print(f"参考图像路径不存在: {reference_path}")
        return None

    if os.path.isfile(reference_path):
        # 单个文件
        if reference_path.endswith('.npy'):
            images = np.load(reference_path)
            if max_images:
                images = images[:max_images]
            return torch.from_numpy(images)
        elif reference_path.endswith('.pt'):
            images = torch.load(reference_path)
            if max_images:
                images = images[:max_images]
            return images

    elif os.path.isdir(reference_path):
        # 目录，加载图像文件
        from PIL import Image

        image_files = []
        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
            image_files.extend([f for f in os.listdir(reference_path) if f.lower().endswith(ext)])

        if not image_files:
            print(f"在目录 {reference_path} 中未找到图像文件")
            return None

        image_files.sort()
        if max_images:
            image_files = image_files[:max_images]

        transform_to_tensor = transforms.Compose([
            transforms.Resize((512, 512)),  # 调整到标准尺寸
            transforms.ToTensor(),
        ])

        images = []
        for img_file in image_files:
            img_path = os.path.join(reference_path, img_file)
            img = Image.open(img_path).convert('RGB')
            img_tensor = transform_to_tensor(img)
            images.append(img_tensor)

        return torch.stack(images)

    return None

def cal_metrics(results_path, device):
    """
    计算图像重建指标，兼容eval.py的实现

    Args:
        results_path: str, 结果文件路径
        device: str, 计算设备

    Returns:
        dict: 包含各项指标的字典
    """
    print(f"开始计算指标，结果路径: {results_path}")

    # Load all images and brain recons
    all_files = [f for f in os.listdir(results_path)]

    number = 0
    all_images, all_brain_recons = [], []
    for file in tqdm(all_files, desc="Loading files"):
        if file.endswith("_img.pt"):
            if file.replace("_img.pt", "_rec.pt") in all_files:
                number += 1
                all_images.append(torch.load(os.path.join(results_path, file), map_location=device))
                all_brain_recons.append(torch.load(os.path.join(results_path, file.replace("_img.pt", "_rec.pt")), map_location=device))

    if number == 0:
        print("未找到匹配的图像对文件 (_img.pt 和 _rec.pt)")
        return None

    all_images = torch.vstack(all_images)
    all_brain_recons = torch.vstack(all_brain_recons)
    all_images = all_images.to(device)
    all_brain_recons = all_brain_recons.to(device).to(all_images.dtype).clamp(0,1).squeeze()

    print("Images shape:", all_images.shape)
    print("Recons shape:", all_brain_recons.shape)
    print("Number:", number)

    results = {}

    try:
        ### PixCorr
        print("\n------calculating pixcorr------")
        preprocess = transforms.Compose([
            transforms.Resize(425, interpolation=transforms.InterpolationMode.BILINEAR),
        ])

        # Flatten images while keeping the batch dimension
        all_images_flattened = preprocess(all_images).reshape(len(all_images), -1).cpu()
        all_brain_recons_flattened = preprocess(all_brain_recons).view(len(all_brain_recons), -1).cpu()

        corrsum = 0
        for i in tqdm(range(number), desc="PixCorr"):
            corrsum += np.corrcoef(all_images_flattened[i], all_brain_recons_flattened[i])[0][1]
        corrmean = corrsum / number

        pixcorr = corrmean
        results['PixCorr'] = pixcorr
        print(f"PixCorr: {pixcorr}")

        del all_images_flattened
        del all_brain_recons_flattened
        torch.cuda.empty_cache()

    except Exception as e:
        print(f"PixCorr计算失败: {e}")
        results['PixCorr'] = None

    try:
        ### SSIM
        print("\n------calculating SSIM------")
        from skimage.color import rgb2gray
        from skimage.metrics import structural_similarity as ssim

        preprocess = transforms.Compose([
            transforms.Resize(425, interpolation=transforms.InterpolationMode.BILINEAR),
        ])

        # convert image to grayscale with rgb2grey
        img_gray = rgb2gray(preprocess(all_images).permute((0,2,3,1)).cpu())
        recon_gray = rgb2gray(preprocess(all_brain_recons).permute((0,2,3,1)).cpu())

        ssim_score=[]
        for im,rec in tqdm(zip(img_gray,recon_gray), total=len(all_images), desc="SSIM"):
            ssim_score.append(ssim(rec, im, multichannel=True, gaussian_weights=True, sigma=1.5, use_sample_covariance=False, data_range=1.0))

        ssim_mean = np.mean(ssim_score)
        results['SSIM'] = ssim_mean
        print(f"SSIM: {ssim_mean}")

    except Exception as e:
        print(f"SSIM计算失败: {e}")
        results['SSIM'] = None

    try:
        #### AlexNet
        print("\n------calculating AlexNet------")
        from torchvision.models import alexnet, AlexNet_Weights
        alex_weights = AlexNet_Weights.IMAGENET1K_V1

        alex_model = create_feature_extractor(alexnet(weights=alex_weights), return_nodes=['features.4','features.11']).to(device)
        alex_model.eval().requires_grad_(False)

        # see alex_weights.transforms()
        preprocess = transforms.Compose([
            transforms.Resize(256, interpolation=transforms.InterpolationMode.BILINEAR),
            transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                std=[0.229, 0.224, 0.225]),
        ])

        # AlexNet(2)
        all_per_correct = two_way_identification(all_brain_recons.to(device).float(), all_images,
                                                 alex_model, preprocess, 'features.4', device=device)
        alexnet2 = np.mean(all_per_correct)
        results['AlexNet(2)'] = alexnet2
        print(f"AlexNet(2) 2-way Percent Correct: {alexnet2:.4f}")

        # AlexNet(5)
        all_per_correct = two_way_identification(all_brain_recons.to(device).float(), all_images,
                                                 alex_model, preprocess, 'features.11', device=device)
        alexnet5 = np.mean(all_per_correct)
        results['AlexNet(5)'] = alexnet5
        print(f"AlexNet(5) 2-way Percent Correct: {alexnet5:.4f}")

        del alex_model
        torch.cuda.empty_cache()

    except Exception as e:
        print(f"AlexNet计算失败: {e}")
        results['AlexNet(2)'] = None
        results['AlexNet(5)'] = None

    try:
        #### InceptionV3
        print("\n------calculating InceptionV3------")
        from torchvision.models import inception_v3, Inception_V3_Weights
        weights = Inception_V3_Weights.DEFAULT
        inception_model = create_feature_extractor(inception_v3(weights=weights),
                                                return_nodes=['avgpool']).to(device)
        inception_model.eval().requires_grad_(False)

        # see weights.transforms()
        preprocess = transforms.Compose([
            transforms.Resize(342, interpolation=transforms.InterpolationMode.BILINEAR),
            transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                std=[0.229, 0.224, 0.225]),
        ])

        all_per_correct = two_way_identification(all_brain_recons, all_images,
                                                inception_model, preprocess, 'avgpool', device=device)

        inception = np.mean(all_per_correct)
        results['InceptionV3'] = inception
        print(f"InceptionV3 2-way Percent Correct: {inception:.4f}")

        del inception_model
        torch.cuda.empty_cache()

    except Exception as e:
        print(f"InceptionV3计算失败: {e}")
        results['InceptionV3'] = None

    try:
        #### CLIP
        print("\n------calculating CLIP------")
        import clip
        clip_model, preprocess = clip.load("ViT-L/14", device=device)

        preprocess = transforms.Compose([
            transforms.Resize(224, interpolation=transforms.InterpolationMode.BILINEAR),
            transforms.Normalize(mean=[0.48145466, 0.4578275, 0.40821073],
                                std=[0.26862954, 0.26130258, 0.27577711]),
        ])

        all_per_correct = two_way_identification(all_brain_recons, all_images,
                                                clip_model.encode_image, preprocess, None, device=device) # final layer
        clip_ = np.mean(all_per_correct)
        results['CLIP'] = clip_
        print(f"CLIP 2-way Percent Correct: {clip_:.4f}")

        del clip_model
        torch.cuda.empty_cache()

    except Exception as e:
        print(f"CLIP计算失败: {e}")
        results['CLIP'] = None

    try:
        #### Efficient Net
        print("\n------calculating EfficientNet------")
        from torchvision.models import efficientnet_b1, EfficientNet_B1_Weights
        weights = EfficientNet_B1_Weights.DEFAULT
        eff_model = create_feature_extractor(efficientnet_b1(weights=weights),
                                            return_nodes=['avgpool']).to(device)
        eff_model.eval().requires_grad_(False)

        # see weights.transforms()
        preprocess = transforms.Compose([
            transforms.Resize(255, interpolation=transforms.InterpolationMode.BILINEAR),
            transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                std=[0.229, 0.224, 0.225]),
        ])

        gt = eff_model(preprocess(all_images))['avgpool']
        gt = gt.reshape(len(gt),-1).cpu().numpy()
        fake = eff_model(preprocess(all_brain_recons))['avgpool']
        fake = fake.reshape(len(fake),-1).cpu().numpy()

        effnet = np.array([sp.spatial.distance.correlation(gt[i],fake[i]) for i in range(len(gt))]).mean()
        results['EffNet-B'] = effnet
        print(f"EffNet-B Distance: {effnet}")

        del eff_model
        torch.cuda.empty_cache()

    except Exception as e:
        print(f"EfficientNet计算失败: {e}")
        results['EffNet-B'] = None

    try:
        #### SwAV
        print("\n------calculating SwAV------")
        swav_model = torch.hub.load('facebookresearch/swav:main', 'resnet50')
        swav_model = create_feature_extractor(swav_model,
                                            return_nodes=['avgpool']).to(device)
        swav_model.eval().requires_grad_(False)

        preprocess = transforms.Compose([
            transforms.Resize(224, interpolation=transforms.InterpolationMode.BILINEAR),
            transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                std=[0.229, 0.224, 0.225]),
        ])

        gt = swav_model(preprocess(all_images))['avgpool']
        gt = gt.reshape(len(gt),-1).cpu().numpy()
        fake = swav_model(preprocess(all_brain_recons))['avgpool']
        fake = fake.reshape(len(fake),-1).cpu().numpy()

        swav = np.array([sp.spatial.distance.correlation(gt[i],fake[i]) for i in range(len(gt))]).mean()
        results['SwAV'] = swav
        print(f"SwAV Distance: {swav}")

        del swav_model
        torch.cuda.empty_cache()

    except Exception as e:
        print(f"SwAV计算失败: {e}")
        results['SwAV'] = None

    # Display results in table
    print("\n" + "="*50)
    print("指标计算结果:")
    print("="*50)

    data = {
        "Metric": ["PixCorr", "SSIM", "AlexNet(2)", "AlexNet(5)", "InceptionV3", "CLIP", "EffNet-B", "SwAV"],
        "Value": [
            results.get('PixCorr', 'N/A'),
            results.get('SSIM', 'N/A'),
            results.get('AlexNet(2)', 'N/A'),
            results.get('AlexNet(5)', 'N/A'),
            results.get('InceptionV3', 'N/A'),
            results.get('CLIP', 'N/A'),
            results.get('EffNet-B', 'N/A'),
            results.get('SwAV', 'N/A')
        ],
    }

    df = pd.DataFrame(data)
    print(df.to_string(index=False))

    # save table to csv file
    csv_path = os.path.join(results_path, f'_metrics_on_{number}samples.csv')
    df.to_csv(csv_path, sep='\t', index=False)
    print(f"\n结果已保存到: {csv_path}")

    return results


# ==================== 主要的生成和指标计算函数 ====================

def generate_images_and_calculate_metrics(
    data_path='evaluation_metrics/brain2clip/eeg2image_sub1&2.npy',
    output_dir='./test/midasnew/EEG',
    reference_images_path=None,
    max_samples=None,
    device='cuda:1',
    num_inference_steps=5,
    img2img_strength=0.0,
    ip_adapter_scale=1,
    # 新增参数
    load_dataset=True,
    dataset_type='fmri',  # 'fmri' 或 'eeg'
    dataset_path='/root/workspace/mindbridge/data/NSD',
    eeg_subject_ids=[1],
    fmri_subject_ids=[1],
    batch_size=200,
    num_workers=4,
    separate_by_subject=True  # 新增参数，控制是否按被试分开保存
):
    """
    生成图像并计算指标的主函数
    
    Args:
        data_path: str, 脑信号嵌入数据路径
        output_dir: str, 输出目录
        reference_images_path: str, 参考图像路径（可选）
        max_samples: int, 最大处理样本数（None表示处理全部）
        device: str, 计算设备
        num_inference_steps: int, 推理步数
        img2img_strength: float, 图像到图像强度
        ip_adapter_scale: float, IP适配器缩放
        load_dataset: bool, 是否从数据集加载参考图像
        dataset_type: str, 数据集类型 ('fmri' 或 'eeg')
        dataset_path: str, 数据集路径
        eeg_subject_ids: list, EEG被试ID列表
        fmri_subject_ids: list, fMRI被试ID列表
        batch_size: int, 批次大小
        num_workers: int, 数据加载线程数
        separate_by_subject: bool, 是否按被试分开保存结果

    Returns:
        dict: 指标计算结果
    """
    print("开始图像生成和指标计算流程...")
    
    # 创建主输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 确定要处理的被试ID列表
    subject_ids = fmri_subject_ids if dataset_type == 'fmri' else eeg_subject_ids
    all_results = {}
    
    # 对每个被试分别处理
    for subj_id in subject_ids:
        print(f"\n{'='*40}")
        print(f"处理被试 {subj_id}")
        print(f"{'='*40}")
        
        # 为每个被试创建单独的输出目录（如果需要）
        if separate_by_subject:
            subj_output_dir = os.path.join(output_dir, f"subj{subj_id}")
            os.makedirs(subj_output_dir, exist_ok=True)
            print(f"被试 {subj_id} 的输出目录: {subj_output_dir}")
        else:
            subj_output_dir = output_dir
        
        # 加载参考图像（如果需要）
        reference_images = None
        if load_dataset:
            print(f"从数据集加载被试 {subj_id} 的参考图像")
            dataloaders = []
            
            if dataset_type == 'fmri':
                dl = load_nsd_dataset(
                    dataset_path, 
                    subj_id, 
                    batch_size=batch_size, 
                    num_workers=num_workers
                )
                dataloaders.append(dl)
            elif dataset_type == 'eeg':
                dl = load_eeg_dataset(
                    subj_id, 
                    batch_size=batch_size, 
                    num_workers=num_workers
                )
                dataloaders.append(dl)
            
            # 提取参考图像
            reference_images = extract_reference_images_from_dataset(
                dataloaders, 
                output_dir=subj_output_dir,  # 保存到被试特定目录
                max_samples=max_samples
            )
        
        # 加载数据 - 这里可能需要根据被试ID选择不同的数据文件
        # 如果数据文件已经按被试分开，则需要修改这部分逻辑
        print(f"加载被试 {subj_id} 的数据: {data_path}")
        h = np.load(data_path)
        if max_samples:
            h = h[:max_samples]
        h = torch.from_numpy(h)
        print(f"数据形状: {h.shape}")
        
        # 设置随机种子
        gen = torch.Generator(device=device)
        gen.manual_seed(1024)
        
        # 创建生成器实例
        print("初始化图像生成器...")
        generator = Generator4Embeds(
            num_inference_steps=num_inference_steps,
            device=device,
            img2img_strength=img2img_strength,
            ip_adapter_scale=ip_adapter_scale
        )
        
        # 生成图像
        print(f"开始为被试 {subj_id} 生成图像...")
        all_generated_images = []
        
        for i in tqdm(range(h.shape[0]), desc=f"为被试 {subj_id} 生成图像"):
            # 提取单个样本，保持 batch 维度
            h_single = h[i:i+1]
            reconstructed_image = generator.generate(h_single.to(dtype=torch.float16), generator=gen)
            
            # 将PIL图像转换为tensor
            transform_to_tensor = transforms.Compose([
                transforms.ToTensor(),
            ])
            
            img_tensor = transform_to_tensor(reconstructed_image)
            
            # 保存PNG格式用于查看
            png_output_path = os.path.join(subj_output_dir, f'{i}.png')
            reconstructed_image.save(png_output_path)
            
            # 保存生成的图像为_rec.pt，使用6位数字格式
            rec_tensor = img_tensor.unsqueeze(0)  # 添加batch维度
            rec_path = os.path.join(subj_output_dir, f'{i:06d}_rec.pt')
            torch.save(rec_tensor, rec_path)
            
            # 记录生成的图像用于可能的后续处理
            all_generated_images.append(img_tensor)
            
            if (i + 1) % 10 == 0:
                print(f"已处理 {i+1}/{h.shape[0]} 个样本")
        
        print(f"被试 {subj_id} 的图像生成完成，共生成 {h.shape[0]} 张图像")
        
        # 计算指标
        print(f"开始计算被试 {subj_id} 的图像指标...")
        subj_results = cal_metrics(subj_output_dir, device)
        all_results[f"subj{subj_id}"] = subj_results
    
    # 汇总所有被试的结果
    print("\n" + "="*60)
    print("所有被试处理完成，汇总结果:")
    print("="*60)
    
    # 创建汇总表格
    summary_data = {
        "Subject": [],
        "PixCorr": [],
        "SSIM": [],
        "AlexNet(2)": [],
        "AlexNet(5)": [],
        "InceptionV3": [],
        "CLIP": [],
        "EffNet-B": [],
        "SwAV": []
    }
    
    for subj_id, results in all_results.items():
        summary_data["Subject"].append(subj_id)
        for metric in ["PixCorr", "SSIM", "AlexNet(2)", "AlexNet(5)", "InceptionV3", "CLIP", "EffNet-B", "SwAV"]:
            value = results.get(metric, None)
            summary_data[metric].append(f"{value:.4f}" if value is not None else "N/A")
    
    summary_df = pd.DataFrame(summary_data)
    print(summary_df.to_string(index=False))
    
    # 保存汇总结果
    summary_path = os.path.join(output_dir, "all_subjects_metrics.csv")
    summary_df.to_csv(summary_path, sep='\t', index=False)
    print(f"\n汇总结果已保存到: {summary_path}")
    
    return all_results


# ==================== 主执行部分 ====================

if __name__ == "__main__":
    # 配置参数
    config = {
        'data_path': 'evaluation_metrics/brain2clip/eeg2image_sub1&2.npy',
        'output_dir': './test/midasnew/EEG_with_metrics',
        'reference_images_path': None,  # 设置为参考图像路径，或None使用虚拟参考图像
        'max_samples': 1000,  # 限制样本数量用于测试，设为None使用全部数据
        'device': 'cuda:1',
        'dataset_type': 'fmri',  # 'fmri' 或 'eeg'
        'num_inference_steps': 5,
        'img2img_strength': 0.0,
        'ip_adapter_scale': 1,
        'eeg_subject_ids': [5, 7],
        'fmri_subject_ids': [1, 2],
    }

    try:
        print("="*60)
        print("开始图像生成和指标计算")
        print("="*60)

        # 生成图像并计算指标
        results = generate_images_and_calculate_metrics(**config)

        print("\n" + "="*60)
        print("处理完成！")
        print("="*60)

        if results:
            print("\n最终指标结果:")
            for metric, value in results.items():
                if value is not None:
                    print(f"  {metric}: {value:.4f}")
                else:
                    print(f"  {metric}: N/A")

    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
