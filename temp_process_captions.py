import json
import os

input_file = 'D:/project/DeCap/generated_image2text_captions.json'
output_file = 'D:/project/DeCap/generated_image2text_captions.txt'

# 确保输入文件存在
if not os.path.exists(input_file):
    print(f"Error: Input file not found at {input_file}")
    exit(1)

try:
    with open(input_file, 'r', encoding='utf-8') as f:
        captions = json.load(f)
except json.JSONDecodeError:
    print(f"Error: Could not decode JSON from {input_file}")
    exit(1)
except Exception as e:
    print(f"An unexpected error occurred while reading {input_file}: {e}")
    exit(1)

with open(output_file, 'w', encoding='utf-8') as f_out:
    for i, caption in enumerate(captions):
        # 移除 <start_of_text> 和 <end_of_text> 标记，并去除首尾空格
        cleaned_caption = caption.replace('<start_of_text>', '').replace('<end_of_text>', '').strip()
        f_out.write(f'{i+1}. {cleaned_caption}\n')

print(f'Captions saved to {output_file}')
